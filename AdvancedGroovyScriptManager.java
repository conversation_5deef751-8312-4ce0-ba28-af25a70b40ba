package com.fenqile.risk.feature.scriptcompute.core.engine.manager;

import com.fenqile.risk.feature.scriptcompute.core.engine.memory.ImprovedMemoryManagementStrategy;
import com.fenqile.risk.feature.scriptcompute.core.engine.utils.GroovyClassLoaderPool;
import groovy.lang.GroovyClassLoader;
import groovy.lang.GroovyObject;
import groovy.lang.Script;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 高级Groovy脚本管理器
 * 实现智能的脚本编译、缓存和内存管理，避免手动GC调用
 */
@Slf4j
@Component
public class AdvancedGroovyScriptManager {
    
    @Autowired
    private GroovyClassLoaderPool classLoaderPool;
    
    @Autowired
    private ImprovedMemoryManagementStrategy memoryStrategy;
    
    @Value("${groovy.script.metaspace.warningThreshold:0.75}")
    private double metaspaceWarningThreshold;
    
    @Value("${groovy.script.metaspace.criticalThreshold:0.85}")
    private double metaspaceCriticalThreshold;
    
    @Value("${groovy.script.monitoring.intervalSeconds:60}")
    private int monitoringIntervalSeconds;
    
    private final ScheduledExecutorService monitorExecutor = Executors.newSingleThreadScheduledExecutor(
            r -> new Thread(r, "groovy-script-monitor"));
    
    private final AtomicLong compilationCount = new AtomicLong(0);
    private final AtomicLong memoryPressureEvents = new AtomicLong(0);
    
    @PostConstruct
    public void init() {
        log.info("Initializing AdvancedGroovyScriptManager");
        memoryStrategy.init();
        
        // 启动元空间监控
        monitorExecutor.scheduleAtFixedRate(this::monitorMetaspaceAndTakeAction, 
                monitoringIntervalSeconds, monitoringIntervalSeconds, TimeUnit.SECONDS);
    }
    
    @PreDestroy
    public void destroy() {
        log.info("Shutting down AdvancedGroovyScriptManager");
        monitorExecutor.shutdown();
        memoryStrategy.shutdown();
    }
    
    /**
     * 智能编译Groovy脚本
     */
    public GroovyObject compileScript(String scriptName, Integer version, String scriptContent) {
        String cacheKey = generateCacheKey(scriptName, version, scriptContent);
        
        // 先尝试从缓存获取
        Object cached = memoryStrategy.getFromCache(cacheKey);
        if (cached instanceof GroovyObject) {
            log.debug("Script cache hit for: {}-{}", scriptName, version);
            return (GroovyObject) cached;
        }
        
        // 缓存未命中，需要编译
        return compileAndCache(cacheKey, scriptContent);
    }
    
    /**
     * 编译并缓存脚本
     */
    private GroovyObject compileAndCache(String cacheKey, String scriptContent) {
        GroovyClassLoader classLoader = null;
        try {
            // 检查元空间压力
            if (isMetaspaceUnderPressure()) {
                handleMetaspacePressure();
            }
            
            classLoader = classLoaderPool.borrowLoader();
            Class<Script> scriptClass = classLoader.parseClass(scriptContent);
            GroovyObject groovyObject = (GroovyObject) scriptClass.newInstance();
            
            // 使用智能缓存策略
            memoryStrategy.putToCache(cacheKey, groovyObject);
            
            compilationCount.incrementAndGet();
            log.debug("Script compiled and cached: {}", cacheKey);
            
            return groovyObject;
            
        } catch (Exception e) {
            log.error("Failed to compile Groovy script: {}", cacheKey, e);
            throw new RuntimeException("Script compilation failed", e);
        } finally {
            if (classLoader != null) {
                classLoaderPool.returnLoader(classLoader);
            }
        }
    }
    
    /**
     * 生成缓存键
     */
    private String generateCacheKey(String scriptName, Integer version, String scriptContent) {
        // 使用脚本名称、版本和内容哈希生成唯一键
        int contentHash = scriptContent.hashCode();
        return String.format("%s-%d-%08x", scriptName, version, contentHash);
    }
    
    /**
     * 检查元空间是否处于压力状态
     */
    private boolean isMetaspaceUnderPressure() {
        try {
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            MemoryUsage metaspaceUsage = memoryBean.getNonHeapMemoryUsage();
            
            if (metaspaceUsage.getMax() > 0) {
                double usageRatio = (double) metaspaceUsage.getUsed() / metaspaceUsage.getMax();
                return usageRatio > metaspaceWarningThreshold;
            }
            
            return false;
        } catch (Exception e) {
            log.warn("Failed to check metaspace pressure", e);
            return false;
        }
    }
    
    /**
     * 处理元空间压力
     */
    private void handleMetaspacePressure() {
        memoryPressureEvents.incrementAndGet();
        log.warn("Metaspace under pressure, performing intelligent cleanup");
        
        // 使用智能内存管理策略处理压力
        memoryStrategy.handleMemoryPressure();
        
        // 清理旧版本脚本缓存
        clearOldVersionCaches();
    }
    
    /**
     * 清理旧版本脚本缓存
     */
    private void clearOldVersionCaches() {
        // 这里可以实现更复杂的版本管理逻辑
        // 例如：保留最新的N个版本，清理其他版本
        log.debug("Clearing old version caches");
    }
    
    /**
     * 监控元空间并采取行动
     */
    private void monitorMetaspaceAndTakeAction() {
        try {
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            MemoryUsage metaspaceUsage = memoryBean.getNonHeapMemoryUsage();
            
            if (metaspaceUsage.getMax() > 0) {
                double usageRatio = (double) metaspaceUsage.getUsed() / metaspaceUsage.getMax();
                long usedMB = metaspaceUsage.getUsed() / 1024 / 1024;
                long maxMB = metaspaceUsage.getMax() / 1024 / 1024;
                
                if (usageRatio > metaspaceCriticalThreshold) {
                    log.error("CRITICAL: Metaspace usage {}% ({}MB/{}MB)", 
                            String.format("%.1f", usageRatio * 100), usedMB, maxMB);
                    handleMetaspacePressure();
                } else if (usageRatio > metaspaceWarningThreshold) {
                    log.warn("WARNING: Metaspace usage {}% ({}MB/{}MB)", 
                            String.format("%.1f", usageRatio * 100), usedMB, maxMB);
                } else {
                    log.debug("Metaspace usage normal: {}% ({}MB/{}MB)", 
                            String.format("%.1f", usageRatio * 100), usedMB, maxMB);
                }
            }
            
        } catch (Exception e) {
            log.error("Error during metaspace monitoring", e);
        }
    }
    
    /**
     * 清理指定特征的所有版本缓存
     */
    public void clearFeatureCaches(String featureName) {
        memoryStrategy.clearCacheByPrefix(featureName + "-");
        log.info("Cleared all caches for feature: {}", featureName);
    }
    
    /**
     * 获取管理器统计信息
     */
    public ManagerStats getStats() {
        ManagerStats stats = new ManagerStats();
        stats.compilationCount = compilationCount.get();
        stats.memoryPressureEvents = memoryPressureEvents.get();
        stats.cacheStats = memoryStrategy.getCacheStats();
        stats.classLoaderPoolStatus = classLoaderPool.getPoolStatus();
        
        return stats;
    }
    
    /**
     * 管理器统计信息类
     */
    public static class ManagerStats {
        public long compilationCount;
        public long memoryPressureEvents;
        public ImprovedMemoryManagementStrategy.CacheStats cacheStats;
        public String classLoaderPoolStatus;
        
        @Override
        public String toString() {
            return String.format("ManagerStats[compilations=%d, pressureEvents=%d, %s, %s]",
                    compilationCount, memoryPressureEvents, cacheStats, classLoaderPoolStatus);
        }
    }
    
    /**
     * 健康检查
     */
    public boolean isHealthy() {
        try {
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            MemoryUsage metaspaceUsage = memoryBean.getNonHeapMemoryUsage();
            
            if (metaspaceUsage.getMax() > 0) {
                double usageRatio = (double) metaspaceUsage.getUsed() / metaspaceUsage.getMax();
                return usageRatio < metaspaceCriticalThreshold;
            }
            
            return true;
        } catch (Exception e) {
            log.error("Health check failed", e);
            return false;
        }
    }
}
