package com.fenqile.risk.feature.scriptcompute.core.engine.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import groovy.lang.GroovyClassLoader;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Groovy类加载器池化管理
 * 用于减少元空间占用和提高类加载器复用率
 */
@Slf4j
@Component
public class GroovyClassLoaderPool {
    
    @Value("${groovy.classloader.pool.maxSize:10}")
    private int maxPoolSize;
    
    @Value("${groovy.classloader.maxClasses:100}")
    private int maxClassesPerLoader;
    
    @Value("${groovy.classloader.cleanup.intervalMinutes:5}")
    private int cleanupIntervalMinutes;
    
    private final Queue<GroovyClassLoader> availableLoaders = new ConcurrentLinkedQueue<>();
    private final Set<GroovyClassLoader> allLoaders = ConcurrentHashMap.newKeySet();
    private final AtomicInteger loaderCount = new AtomicInteger(0);
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor(
            r -> new Thread(r, "groovy-classloader-cleanup"));
    
    @PostConstruct
    public void init() {
        log.info("Initializing GroovyClassLoaderPool with maxSize: {}, maxClassesPerLoader: {}", 
                maxPoolSize, maxClassesPerLoader);
        
        // 启动定期清理任务
        cleanupExecutor.scheduleAtFixedRate(this::performCleanup, 
                cleanupIntervalMinutes, cleanupIntervalMinutes, TimeUnit.MINUTES);
    }
    
    @PreDestroy
    public void destroy() {
        log.info("Destroying GroovyClassLoaderPool");
        cleanupExecutor.shutdown();
        
        // 关闭所有类加载器
        allLoaders.forEach(this::closeLoader);
        allLoaders.clear();
        availableLoaders.clear();
    }
    
    /**
     * 借用一个类加载器
     */
    public GroovyClassLoader borrowLoader() {
        GroovyClassLoader loader = availableLoaders.poll();
        
        if (loader == null) {
            if (loaderCount.get() < maxPoolSize) {
                loader = createNewLoader();
                log.debug("Created new GroovyClassLoader, total count: {}", loaderCount.get());
            } else {
                // 池已满，等待或创建临时加载器
                log.warn("GroovyClassLoader pool is full, creating temporary loader");
                return new GroovyClassLoader();
            }
        }
        
        return loader;
    }
    
    /**
     * 归还类加载器
     */
    public void returnLoader(GroovyClassLoader loader) {
        if (loader == null) {
            return;
        }
        
        // 检查是否是池中的加载器
        if (!allLoaders.contains(loader)) {
            // 临时加载器，直接关闭
            closeLoader(loader);
            return;
        }
        
        // 检查类数量，超过阈值则销毁
        if (getLoadedClassCount(loader) > maxClassesPerLoader) {
            log.info("GroovyClassLoader has too many classes ({}), destroying it", 
                    getLoadedClassCount(loader));
            destroyLoader(loader);
        } else {
            // 清理缓存但保留加载器
            loader.clearCache();
            availableLoaders.offer(loader);
        }
    }
    
    /**
     * 创建新的类加载器
     */
    private GroovyClassLoader createNewLoader() {
        GroovyClassLoader loader = new GroovyClassLoader();
        allLoaders.add(loader);
        loaderCount.incrementAndGet();
        return loader;
    }
    
    /**
     * 销毁类加载器
     */
    private void destroyLoader(GroovyClassLoader loader) {
        if (allLoaders.remove(loader)) {
            loaderCount.decrementAndGet();
            closeLoader(loader);
            log.debug("Destroyed GroovyClassLoader, remaining count: {}", loaderCount.get());
        }
    }
    
    /**
     * 关闭类加载器
     */
    private void closeLoader(GroovyClassLoader loader) {
        try {
            loader.clearCache();
            loader.close();
        } catch (IOException e) {
            log.warn("Failed to close GroovyClassLoader", e);
        }
    }
    
    /**
     * 获取加载器中的类数量
     */
    private int getLoadedClassCount(GroovyClassLoader loader) {
        try {
            return loader.getLoadedClasses().length;
        } catch (Exception e) {
            log.warn("Failed to get loaded class count", e);
            return 0;
        }
    }
    
    /**
     * 定期清理任务
     */
    private void performCleanup() {
        try {
            log.debug("Performing GroovyClassLoader cleanup");
            
            // 监控元空间使用情况
            monitorMetaspaceUsage();
            
            // 清理过期的类加载器
            cleanupExpiredLoaders();
            
            // 触发GC以卸载未使用的类
            System.gc();
            
        } catch (Exception e) {
            log.error("Error during GroovyClassLoader cleanup", e);
        }
    }
    
    /**
     * 监控元空间使用情况
     */
    private void monitorMetaspaceUsage() {
        try {
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            MemoryUsage metaspaceUsage = memoryBean.getNonHeapMemoryUsage();
            
            long used = metaspaceUsage.getUsed();
            long max = metaspaceUsage.getMax();
            double usageRatio = max > 0 ? (double) used / max : 0;
            
            log.info("Metaspace usage: {}MB / {}MB ({}%), Active loaders: {}", 
                    used / 1024 / 1024, 
                    max > 0 ? max / 1024 / 1024 : "unlimited",
                    String.format("%.2f", usageRatio * 100),
                    loaderCount.get());
            
            // 高使用率告警
            if (usageRatio > 0.8) {
                log.warn("Metaspace usage is high: {}%, consider increasing MaxMetaspaceSize", 
                        String.format("%.2f", usageRatio * 100));
            }
            
        } catch (Exception e) {
            log.warn("Failed to monitor metaspace usage", e);
        }
    }
    
    /**
     * 清理过期的类加载器
     */
    private void cleanupExpiredLoaders() {
        // 如果可用加载器过多，销毁一些
        while (availableLoaders.size() > maxPoolSize / 2 && !availableLoaders.isEmpty()) {
            GroovyClassLoader loader = availableLoaders.poll();
            if (loader != null) {
                destroyLoader(loader);
            }
        }
    }
    
    /**
     * 获取池状态信息
     */
    public String getPoolStatus() {
        return String.format("GroovyClassLoaderPool[total=%d, available=%d, maxSize=%d]",
                loaderCount.get(), availableLoaders.size(), maxPoolSize);
    }
}
