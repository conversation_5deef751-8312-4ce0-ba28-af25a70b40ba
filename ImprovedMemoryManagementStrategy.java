package com.fenqile.risk.feature.scriptcompute.core.engine.memory;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.ref.WeakReference;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 改进的内存管理策略
 * 不依赖手动GC，通过更智能的缓存管理和资源释放来优化元空间使用
 */
@Slf4j
@Component
public class ImprovedMemoryManagementStrategy {
    
    @Value("${memory.management.maxCacheSize:1000}")
    private int maxCacheSize;
    
    @Value("${memory.management.cleanupIntervalMinutes:10}")
    private int cleanupIntervalMinutes;
    
    @Value("${memory.management.maxIdleTimeMinutes:30}")
    private int maxIdleTimeMinutes;
    
    // 使用WeakReference缓存，允许JVM在内存压力时自动回收
    private final Map<String, WeakReference<Object>> weakCache = new ConcurrentHashMap<>();
    
    // 记录缓存项的最后访问时间
    private final Map<String, Long> accessTimeMap = new ConcurrentHashMap<>();
    
    // 统计信息
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);
    private final AtomicLong evictions = new AtomicLong(0);
    
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor(
            r -> new Thread(r, "memory-management-cleanup"));
    
    public void init() {
        log.info("Initializing ImprovedMemoryManagementStrategy with maxCacheSize: {}", maxCacheSize);
        
        // 启动定期清理任务
        cleanupExecutor.scheduleAtFixedRate(this::performIntelligentCleanup, 
                cleanupIntervalMinutes, cleanupIntervalMinutes, TimeUnit.MINUTES);
    }
    
    /**
     * 智能缓存获取
     */
    public Object getFromCache(String key) {
        WeakReference<Object> ref = weakCache.get(key);
        if (ref != null) {
            Object value = ref.get();
            if (value != null) {
                // 更新访问时间
                accessTimeMap.put(key, System.currentTimeMillis());
                cacheHits.incrementAndGet();
                return value;
            } else {
                // WeakReference已被回收，清理相关记录
                weakCache.remove(key);
                accessTimeMap.remove(key);
            }
        }
        
        cacheMisses.incrementAndGet();
        return null;
    }
    
    /**
     * 智能缓存存储
     */
    public void putToCache(String key, Object value) {
        // 检查缓存大小限制
        if (weakCache.size() >= maxCacheSize) {
            performSizeBasedEviction();
        }
        
        weakCache.put(key, new WeakReference<>(value));
        accessTimeMap.put(key, System.currentTimeMillis());
    }
    
    /**
     * 基于大小的缓存驱逐
     */
    private void performSizeBasedEviction() {
        long currentTime = System.currentTimeMillis();
        long maxIdleTime = maxIdleTimeMinutes * 60 * 1000L;
        
        // 优先驱逐最久未访问的项
        accessTimeMap.entrySet().stream()
                .filter(entry -> currentTime - entry.getValue() > maxIdleTime)
                .limit(maxCacheSize / 4) // 一次最多驱逐25%
                .forEach(entry -> {
                    String key = entry.getKey();
                    weakCache.remove(key);
                    accessTimeMap.remove(key);
                    evictions.incrementAndGet();
                });
        
        log.debug("Performed size-based eviction, cache size: {}", weakCache.size());
    }
    
    /**
     * 智能清理任务
     */
    private void performIntelligentCleanup() {
        try {
            log.debug("Performing intelligent memory cleanup");
            
            long startTime = System.currentTimeMillis();
            int initialSize = weakCache.size();
            
            // 清理已被GC回收的WeakReference
            cleanupExpiredReferences();
            
            // 清理长时间未访问的缓存项
            cleanupIdleEntries();
            
            long endTime = System.currentTimeMillis();
            int finalSize = weakCache.size();
            
            log.info("Intelligent cleanup completed in {}ms, cache size: {} -> {}, evictions: {}",
                    endTime - startTime, initialSize, finalSize, evictions.get());
            
        } catch (Exception e) {
            log.error("Error during intelligent cleanup", e);
        }
    }
    
    /**
     * 清理已过期的WeakReference
     */
    private void cleanupExpiredReferences() {
        weakCache.entrySet().removeIf(entry -> {
            if (entry.getValue().get() == null) {
                accessTimeMap.remove(entry.getKey());
                return true;
            }
            return false;
        });
    }
    
    /**
     * 清理长时间未访问的缓存项
     */
    private void cleanupIdleEntries() {
        long currentTime = System.currentTimeMillis();
        long maxIdleTime = maxIdleTimeMinutes * 60 * 1000L;
        
        accessTimeMap.entrySet().removeIf(entry -> {
            if (currentTime - entry.getValue() > maxIdleTime) {
                weakCache.remove(entry.getKey());
                evictions.incrementAndGet();
                return true;
            }
            return false;
        });
    }
    
    /**
     * 主动清理指定前缀的缓存项
     */
    public void clearCacheByPrefix(String prefix) {
        int removedCount = 0;
        
        for (String key : weakCache.keySet()) {
            if (key.startsWith(prefix)) {
                weakCache.remove(key);
                accessTimeMap.remove(key);
                removedCount++;
            }
        }
        
        log.info("Cleared {} cache entries with prefix: {}", removedCount, prefix);
    }
    
    /**
     * 获取缓存统计信息
     */
    public CacheStats getCacheStats() {
        // 清理过期引用以获得准确的大小
        cleanupExpiredReferences();
        
        CacheStats stats = new CacheStats();
        stats.size = weakCache.size();
        stats.hits = cacheHits.get();
        stats.misses = cacheMisses.get();
        stats.evictions = evictions.get();
        stats.hitRate = stats.hits + stats.misses > 0 ? 
                (double) stats.hits / (stats.hits + stats.misses) : 0;
        
        return stats;
    }
    
    /**
     * 检查内存压力并采取相应措施
     */
    public void handleMemoryPressure() {
        log.warn("Handling memory pressure, performing aggressive cleanup");
        
        // 清理所有过期引用
        cleanupExpiredReferences();
        
        // 更激进的空闲项清理（减少空闲时间阈值）
        long currentTime = System.currentTimeMillis();
        long aggressiveIdleTime = 5 * 60 * 1000L; // 5分钟
        
        accessTimeMap.entrySet().removeIf(entry -> {
            if (currentTime - entry.getValue() > aggressiveIdleTime) {
                weakCache.remove(entry.getKey());
                evictions.incrementAndGet();
                return true;
            }
            return false;
        });
        
        log.info("Memory pressure handling completed, cache size: {}", weakCache.size());
    }
    
    /**
     * 缓存统计信息类
     */
    public static class CacheStats {
        public int size;
        public long hits;
        public long misses;
        public long evictions;
        public double hitRate;
        
        @Override
        public String toString() {
            return String.format("CacheStats[size=%d, hits=%d, misses=%d, evictions=%d, hitRate=%.2f%%]",
                    size, hits, misses, evictions, hitRate * 100);
        }
    }
    
    /**
     * 关闭资源
     */
    public void shutdown() {
        cleanupExecutor.shutdown();
        weakCache.clear();
        accessTimeMap.clear();
        log.info("ImprovedMemoryManagementStrategy shutdown completed");
    }
}
