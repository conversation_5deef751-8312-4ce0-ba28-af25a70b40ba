package com.fenqile.risk.feature.scriptcompute.core.engine.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.fenqile.risk.feature.scriptcompute.core.engine.utils.GroovyClassLoaderPool;
import com.fenqile.risk.feature.scriptcompute.core.engine.utils.OptimizedGroovyUtil;

import java.lang.management.ClassLoadingMXBean;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 元空间监控组件
 * 监控元空间使用情况，提供告警和自动清理功能
 */
@Slf4j
@Component
public class MetaspaceMonitor {
    
    @Autowired
    private GroovyClassLoaderPool classLoaderPool;
    
    @Autowired
    private OptimizedGroovyUtil groovyUtil;
    
    @Value("${metaspace.monitor.warningThreshold:0.8}")
    private double warningThreshold;
    
    @Value("${metaspace.monitor.criticalThreshold:0.9}")
    private double criticalThreshold;
    
    @Value("${metaspace.monitor.autoCleanup:true}")
    private boolean autoCleanupEnabled;
    
    private final AtomicLong lastWarningTime = new AtomicLong(0);
    private final AtomicLong lastCriticalTime = new AtomicLong(0);
    private final AtomicLong cleanupCount = new AtomicLong(0);
    
    /**
     * 定期监控元空间使用情况（每30秒）
     */
    @Scheduled(fixedRate = 30000)
    public void monitorMetaspace() {
        try {
            MetaspaceInfo info = getMetaspaceInfo();
            logMetaspaceStatus(info);
            
            // 检查告警阈值
            checkThresholds(info);
            
        } catch (Exception e) {
            log.error("Error during metaspace monitoring", e);
        }
    }
    
    /**
     * 详细的元空间监控（每5分钟）
     */
    @Scheduled(fixedRate = 300000)
    public void detailedMonitoring() {
        try {
            MetaspaceInfo info = getMetaspaceInfo();
            ClassLoadingInfo classInfo = getClassLoadingInfo();
            
            log.info("=== Detailed Metaspace Report ===");
            log.info("Metaspace: used={}MB, committed={}MB, max={}MB, usage={}%",
                    info.usedMB, info.committedMB, info.maxMB, 
                    String.format("%.2f", info.usageRatio * 100));
            
            log.info("Classes: loaded={}, unloaded={}, current={}",
                    classInfo.totalLoaded, classInfo.totalUnloaded, classInfo.currentLoaded);
            
            log.info("GroovyClassLoader: {}", classLoaderPool.getPoolStatus());
            log.info("ScriptCache: {}", groovyUtil.getCacheStats());
            
            // 如果使用率过高且启用自动清理，执行清理
            if (info.usageRatio > criticalThreshold && autoCleanupEnabled) {
                performEmergencyCleanup();
            }
            
        } catch (Exception e) {
            log.error("Error during detailed metaspace monitoring", e);
        }
    }
    
    /**
     * 获取元空间信息
     */
    private MetaspaceInfo getMetaspaceInfo() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage metaspaceUsage = memoryBean.getNonHeapMemoryUsage();
        
        MetaspaceInfo info = new MetaspaceInfo();
        info.used = metaspaceUsage.getUsed();
        info.committed = metaspaceUsage.getCommitted();
        info.max = metaspaceUsage.getMax();
        info.usedMB = info.used / 1024 / 1024;
        info.committedMB = info.committed / 1024 / 1024;
        info.maxMB = info.max > 0 ? info.max / 1024 / 1024 : -1;
        info.usageRatio = info.max > 0 ? (double) info.used / info.max : 0;
        
        return info;
    }
    
    /**
     * 获取类加载信息
     */
    private ClassLoadingInfo getClassLoadingInfo() {
        ClassLoadingMXBean classLoadingBean = ManagementFactory.getClassLoadingMXBean();
        
        ClassLoadingInfo info = new ClassLoadingInfo();
        info.currentLoaded = classLoadingBean.getLoadedClassCount();
        info.totalLoaded = classLoadingBean.getTotalLoadedClassCount();
        info.totalUnloaded = classLoadingBean.getUnloadedClassCount();
        
        return info;
    }
    
    /**
     * 记录元空间状态
     */
    private void logMetaspaceStatus(MetaspaceInfo info) {
        if (log.isDebugEnabled()) {
            log.debug("Metaspace usage: {}MB / {}MB ({}%)",
                    info.usedMB, 
                    info.maxMB > 0 ? info.maxMB : "unlimited",
                    String.format("%.2f", info.usageRatio * 100));
        }
    }
    
    /**
     * 检查告警阈值
     */
    private void checkThresholds(MetaspaceInfo info) {
        long currentTime = System.currentTimeMillis();
        
        if (info.usageRatio > criticalThreshold) {
            // 临界告警（每10分钟最多一次）
            if (currentTime - lastCriticalTime.get() > 600000) {
                log.error("CRITICAL: Metaspace usage is {}% (threshold: {}%), " +
                        "used: {}MB, max: {}MB",
                        String.format("%.2f", info.usageRatio * 100),
                        String.format("%.0f", criticalThreshold * 100),
                        info.usedMB, info.maxMB);
                lastCriticalTime.set(currentTime);
            }
        } else if (info.usageRatio > warningThreshold) {
            // 警告告警（每5分钟最多一次）
            if (currentTime - lastWarningTime.get() > 300000) {
                log.warn("WARNING: Metaspace usage is {}% (threshold: {}%), " +
                        "used: {}MB, max: {}MB",
                        String.format("%.2f", info.usageRatio * 100),
                        String.format("%.0f", warningThreshold * 100),
                        info.usedMB, info.maxMB);
                lastWarningTime.set(currentTime);
            }
        }
    }
    
    /**
     * 执行紧急清理
     */
    private void performEmergencyCleanup() {
        long count = cleanupCount.incrementAndGet();
        log.warn("Performing emergency cleanup #{} due to high metaspace usage", count);
        
        try {
            // 清理脚本缓存
            groovyUtil.clearScriptCache();
            
            // 触发GC
            System.gc();
            
            // 等待一段时间让GC完成
            Thread.sleep(1000);
            
            // 检查清理效果
            MetaspaceInfo afterCleanup = getMetaspaceInfo();
            log.info("Emergency cleanup completed. Metaspace usage: {}% -> {}%",
                    String.format("%.2f", criticalThreshold * 100),
                    String.format("%.2f", afterCleanup.usageRatio * 100));
            
        } catch (Exception e) {
            log.error("Error during emergency cleanup", e);
        }
    }
    
    /**
     * 手动触发清理
     */
    public void manualCleanup() {
        log.info("Manual cleanup triggered");
        performEmergencyCleanup();
    }
    
    /**
     * 获取监控统计信息
     */
    public String getMonitorStats() {
        MetaspaceInfo info = getMetaspaceInfo();
        ClassLoadingInfo classInfo = getClassLoadingInfo();
        
        return String.format("MetaspaceMonitor[usage=%.2f%%, classes=%d, cleanups=%d]",
                info.usageRatio * 100, classInfo.currentLoaded, cleanupCount.get());
    }
    
    /**
     * 元空间信息类
     */
    private static class MetaspaceInfo {
        long used;
        long committed;
        long max;
        long usedMB;
        long committedMB;
        long maxMB;
        double usageRatio;
    }
    
    /**
     * 类加载信息类
     */
    private static class ClassLoadingInfo {
        int currentLoaded;
        long totalLoaded;
        long totalUnloaded;
    }
}
