package com.fenqile.risk.feature.scriptcompute.core.engine.utils;

import com.fenqile.risk.feature.scriptcompute.core.engine.exception.ScriptExecuteException;
import groovy.lang.GroovyClassLoader;
import groovy.lang.GroovyObject;
import groovy.lang.Script;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 优化后的Groovy工具类
 * 使用类加载器池化和改进的缓存机制
 */
@Slf4j
@Component
public class OptimizedGroovyUtil {
    
    @Autowired
    private GroovyClassLoaderPool classLoaderPool;
    
    // 脚本编译统计
    private final AtomicLong compileCount = new AtomicLong(0);
    private final AtomicLong cacheHitCount = new AtomicLong(0);
    
    // 脚本内容到类的缓存（使用MD5作为key）
    private final Map<String, Class<Script>> scriptClassCache = new ConcurrentHashMap<>();
    
    /**
     * 编译Groovy脚本并获取GroovyObject实例
     */
    public GroovyObject compileGroovyScriptAndGet(String scriptText) {
        Class<Script> groovyClass = compileGroovyScript(scriptText);
        try {
            return (GroovyObject) groovyClass.newInstance();
        } catch (Exception e) {
            throw new ScriptExecuteException("groovy script execute error", e);
        }
    }
    
    /**
     * 编译Groovy脚本
     * 使用内容哈希作为缓存key，避免重复编译相同内容的脚本
     */
    public Class<Script> compileGroovyScript(String scriptText) {
        // 计算脚本内容的哈希值作为缓存key
        String cacheKey = calculateScriptHash(scriptText);
        
        // 先检查缓存
        Class<Script> cachedClass = scriptClassCache.get(cacheKey);
        if (cachedClass != null) {
            cacheHitCount.incrementAndGet();
            log.debug("Script cache hit for key: {}", cacheKey);
            return cachedClass;
        }
        
        // 缓存未命中，需要编译
        compileCount.incrementAndGet();
        log.debug("Compiling script, cache key: {}", cacheKey);
        
        GroovyClassLoader classLoader = null;
        try {
            classLoader = classLoaderPool.borrowLoader();
            Class<Script> scriptClass = classLoader.parseClass(scriptText);
            
            // 缓存编译结果
            scriptClassCache.put(cacheKey, scriptClass);
            
            log.debug("Script compiled and cached, key: {}", cacheKey);
            return scriptClass;
            
        } catch (Exception e) {
            log.error("Failed to compile Groovy script", e);
            throw new ScriptExecuteException("Failed to compile Groovy script", e);
        } finally {
            if (classLoader != null) {
                classLoaderPool.returnLoader(classLoader);
            }
        }
    }
    
    /**
     * 计算脚本内容的哈希值
     */
    private String calculateScriptHash(String scriptText) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(scriptText.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            log.warn("Failed to calculate script hash, using hashCode", e);
            return String.valueOf(scriptText.hashCode());
        }
    }
    
    /**
     * 清理脚本缓存
     * 可以在内存压力大时调用
     */
    public void clearScriptCache() {
        int size = scriptClassCache.size();
        scriptClassCache.clear();
        log.info("Cleared script class cache, removed {} entries", size);
    }
    
    /**
     * 清理特定脚本的缓存
     */
    public void clearScriptCache(String scriptText) {
        String cacheKey = calculateScriptHash(scriptText);
        if (scriptClassCache.remove(cacheKey) != null) {
            log.debug("Removed script from cache, key: {}", cacheKey);
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        long total = compileCount.get() + cacheHitCount.get();
        double hitRate = total > 0 ? (double) cacheHitCount.get() / total * 100 : 0;
        
        return String.format("ScriptCache[size=%d, compiles=%d, hits=%d, hitRate=%.2f%%]",
                scriptClassCache.size(), compileCount.get(), cacheHitCount.get(), hitRate);
    }
    
    /**
     * 获取缓存大小
     */
    public int getCacheSize() {
        return scriptClassCache.size();
    }
    
    /**
     * 检查缓存中是否存在指定脚本
     */
    public boolean isScriptCached(String scriptText) {
        String cacheKey = calculateScriptHash(scriptText);
        return scriptClassCache.containsKey(cacheKey);
    }
}
