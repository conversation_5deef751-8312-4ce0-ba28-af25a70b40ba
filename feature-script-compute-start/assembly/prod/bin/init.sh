#!/bin/bash

source /home/<USER>/dubbo_framework/dubbo_shell/dubbo_init.sh

# timeout config , if application stop timeout force kill process
export STOP_TIMEOUT=10
# jvm memory config - optimized for Groovy script execution
export JAVA_MEM_OPTS="-server -Xms8192m -Xmx8192m \
-XX:MetaspaceSize=1024m -XX:MaxMetaspaceSize=2048m -XX:CompressedClassSpaceSize=256m \
-XX:MaxGCPauseMillis=200 -XX:SurvivorRatio=8 -XX:NewRatio=2 \
-XX:+UseG1GC -XX:G1HeapRegionSize=16m -XX:G1ReservePercent=15 -XX:InitiatingHeapOccupancyPercent=45 \
-XX:+ClassUnloadingWithConcurrentMark -XX:+ExplicitGCInvokesConcurrent \
-XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintClassHistogram \
-XX:-OmitStackTraceInFastThrow -Xloggc:/home/<USER>/logs/gc.log"


#use base_component_all
export USE_BASE_COMPONENT_ALL=1
