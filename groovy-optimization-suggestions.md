# Groovy脚本元空间优化建议

## 1. 类加载器优化

### 1.1 实现类加载器池化管理

```java
@Component
public class GroovyClassLoaderPool {
    private final Queue<GroovyClassLoader> availableLoaders = new ConcurrentLinkedQueue<>();
    private final Set<GroovyClassLoader> allLoaders = ConcurrentHashMap.newKeySet();
    private final AtomicInteger loaderCount = new AtomicInteger(0);
    private final int maxPoolSize = 10; // 可配置
    
    public GroovyClassLoader borrowLoader() {
        GroovyClassLoader loader = availableLoaders.poll();
        if (loader == null && loaderCount.get() < maxPoolSize) {
            loader = createNewLoader();
        }
        return loader;
    }
    
    public void returnLoader(GroovyClassLoader loader) {
        // 检查类数量，超过阈值则销毁
        if (loader.getLoadedClasses().length > 100) {
            destroyLoader(loader);
        } else {
            availableLoaders.offer(loader);
        }
    }
    
    private void destroyLoader(GroovyClassLoader loader) {
        allLoaders.remove(loader);
        loaderCount.decrementAndGet();
        try {
            loader.close(); // 关闭类加载器
        } catch (IOException e) {
            log.warn("Failed to close GroovyClassLoader", e);
        }
    }
}
```

### 1.2 优化GroovyUtil类

```java
public class GroovyUtil {
    private static final GroovyClassLoaderPool loaderPool = new GroovyClassLoaderPool();
    
    public static Class<Script> compileGroovyScript(String scriptText) {
        GroovyClassLoader classLoader = loaderPool.borrowLoader();
        try {
            return classLoader.parseClass(scriptText);
        } finally {
            loaderPool.returnLoader(classLoader);
        }
    }
}
```

## 2. 脚本缓存优化

### 2.1 实现基于WeakReference的缓存

```java
public class OptimizedScriptCache {
    private final Map<String, WeakReference<GroovyScript>> scriptCache = new ConcurrentHashMap<>();
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor();
    
    public OptimizedScriptCache() {
        // 定期清理失效的WeakReference
        cleanupExecutor.scheduleAtFixedRate(this::cleanupExpiredReferences, 5, 5, TimeUnit.MINUTES);
    }
    
    public GroovyScript getScript(String key) {
        WeakReference<GroovyScript> ref = scriptCache.get(key);
        if (ref != null) {
            GroovyScript script = ref.get();
            if (script != null) {
                return script;
            } else {
                scriptCache.remove(key); // 清理失效引用
            }
        }
        return null;
    }
    
    public void putScript(String key, GroovyScript script) {
        scriptCache.put(key, new WeakReference<>(script));
    }
    
    private void cleanupExpiredReferences() {
        scriptCache.entrySet().removeIf(entry -> entry.getValue().get() == null);
    }
}
```

### 2.2 实现版本化缓存清理

```java
@Component
public class VersionedScriptManager {
    private final Map<String, Set<String>> featureVersions = new ConcurrentHashMap<>();
    
    public void registerScript(String featureName, Integer versionNo, String cacheKey) {
        String feature = featureName;
        featureVersions.computeIfAbsent(feature, k -> ConcurrentHashMap.newKeySet()).add(cacheKey);
    }
    
    public void cleanupOldVersions(String featureName, Integer currentVersion) {
        Set<String> versions = featureVersions.get(featureName);
        if (versions != null) {
            versions.stream()
                .filter(key -> !key.contains("-" + currentVersion + "-"))
                .forEach(oldKey -> {
                    GlobalCache.removeScript(oldKey);
                    versions.remove(oldKey);
                });
        }
    }
}
```

## 3. 类卸载机制

### 3.1 实现定期类卸载

```java
@Component
public class GroovyClassUnloader {
    private final ScheduledExecutorService unloadExecutor = Executors.newSingleThreadScheduledExecutor();
    
    @PostConstruct
    public void startUnloadScheduler() {
        unloadExecutor.scheduleAtFixedRate(this::performClassUnload, 10, 10, TimeUnit.MINUTES);
    }
    
    private void performClassUnload() {
        // 触发Full GC以卸载未使用的类
        System.gc();
        
        // 记录元空间使用情况
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage metaspaceUsage = memoryBean.getNonHeapMemoryUsage();
        log.info("Metaspace usage after GC: used={}, max={}", 
                metaspaceUsage.getUsed(), metaspaceUsage.getMax());
    }
}
```

## 4. JVM参数优化建议

### 4.1 元空间相关参数

```bash
# 当前配置
-XX:MetaspaceSize=512m

# 优化建议
-XX:MetaspaceSize=1024m          # 增加初始元空间大小
-XX:MaxMetaspaceSize=2048m       # 设置最大元空间大小
-XX:CompressedClassSpaceSize=256m # 压缩类空间大小
```

### 4.2 GC优化参数

```bash
# 添加以下参数
-XX:+UnlockExperimentalVMOptions
-XX:+UseG1GC                     # 已有
-XX:+ClassUnloadingWithConcurrentMark  # 启用并发标记类卸载
-XX:+CMSClassUnloadingEnabled    # 如果使用CMS GC
-XX:+ExplicitGCInvokesConcurrent # 显式GC使用并发收集
```

### 4.3 监控参数

```bash
# 添加监控参数
-XX:+PrintGCDetails
-XX:+PrintGCTimeStamps
-XX:+PrintClassHistogram
-XX:+TraceClassLoading
-XX:+TraceClassUnloading
-Xloggc:/path/to/gc.log
```

## 5. 监控和告警

### 5.1 元空间使用率监控

```java
@Component
public class MetaspaceMonitor {
    private final MeterRegistry meterRegistry;
    
    @Scheduled(fixedRate = 30000) // 30秒检查一次
    public void monitorMetaspace() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage metaspaceUsage = memoryBean.getNonHeapMemoryUsage();
        
        double usageRatio = (double) metaspaceUsage.getUsed() / metaspaceUsage.getMax();
        
        // 上报监控指标
        meterRegistry.gauge("metaspace.usage.ratio", usageRatio);
        
        // 告警阈值
        if (usageRatio > 0.8) {
            log.warn("Metaspace usage is high: {}%", usageRatio * 100);
            // 触发告警
        }
    }
}
```

## 6. 实施步骤

1. **第一阶段**：实现类加载器池化和优化缓存机制
2. **第二阶段**：添加类卸载机制和版本管理
3. **第三阶段**：调整JVM参数和添加监控
4. **第四阶段**：性能测试和参数微调

## 7. 预期效果

- 元空间使用率降低30-50%
- 减少Full GC频率
- 提高脚本编译和执行性能
- 增强系统稳定性
